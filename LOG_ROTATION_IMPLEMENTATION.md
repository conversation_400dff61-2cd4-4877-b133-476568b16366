# GPU Governor 日志轮转机制实现

## 概述

为GPU Governor Rust程序实现了完整的日志轮转机制，限制`/data/adb/gpu_governor/log/gpu_gov.log`文件大小不超过7MB。

## 实现特性

### 1. 日志大小限制
- **最大文件大小**: 7MB
- **轮转阈值**: 80%（即5.6MB时触发轮转）
- **备份策略**: 保留一个`.bak`备份文件

### 2. 轮转机制
- **自动轮转**: 每5分钟检查一次日志文件大小
- **手动轮转**: 提供API支持手动触发轮转
- **安全轮转**: 轮转失败不会影响程序正常运行

### 3. 双重输出
- **控制台输出**: 保持原有的控制台日志输出
- **文件输出**: 同时写入到日志文件，支持轮转

## 代码修改详情

### 1. 核心日志模块 (`src/utils/logger.rs`)

#### 主要变更：
- 将`CustomLogger`从仅控制台输出改为支持文件输出
- 添加了`BufWriter`用于高效文件写入
- 实现了自动日志轮转检查
- 添加了线程安全的文件写入机制

#### 新增功能：
```rust
// 手动触发日志轮转
pub fn rotate_log_now() -> Result<()>

// 获取当前日志文件大小（MB）
pub fn get_log_file_size_mb() -> Result<f64>

// 检查是否需要日志轮转
pub fn should_rotate_log() -> Result<bool>
```

### 2. 日志轮转管理器 (`src/utils/log_rotation.rs`)

#### 新增模块，提供：
- `LogRotationManager`: 核心轮转管理类
- 配置化的轮转参数（大小限制、阈值）
- 完整的轮转操作（检查、备份、创建新文件）
- 单元测试支持

#### 主要API：
```rust
// 检查并执行轮转
pub fn check_and_rotate_main_log() -> Result<bool>

// 强制轮转
pub fn force_rotate_main_log() -> Result<()>

// 获取日志大小
pub fn get_main_log_size_mb() -> Result<f64>
```

### 3. 主程序集成 (`src/main.rs`)

#### 添加了日志轮转监控线程：
- 每5分钟自动检查日志文件大小
- 自动执行轮转操作
- 错误处理不影响主程序运行

### 4. Shell脚本更新

#### 更新了所有相关脚本的日志大小限制：
- `module/action.sh`: `MAX_LOG_SIZE_MB=7`
- `module/script/initsvc.sh`: `MAX_LOG_SIZE_MB=7`
- `module/script/libcommon.sh`: 默认值改为7MB
- `module/webroot/app.js`: `MAX_LOG_SIZE_MB = 7`

## 轮转流程

### 自动轮转流程：
1. **监控线程**: 每5分钟检查一次日志文件大小
2. **阈值检查**: 当文件大小超过5.6MB（80%阈值）时触发
3. **备份操作**: 将当前日志文件重命名为`.bak`
4. **新建文件**: 创建新的日志文件并写入轮转信息
5. **继续记录**: 新的日志消息写入到新文件中

### 轮转时机：
- **启动时**: 程序启动后5分钟开始监控
- **运行时**: 每5分钟检查一次
- **写入时**: 每次写入日志前检查（在logger中）
- **手动**: 可通过API手动触发

## 错误处理

### 容错机制：
- **轮转失败**: 不影响程序继续运行，只记录警告
- **文件权限**: 自动设置正确的文件权限
- **磁盘空间**: 轮转前检查，失败时继续使用原文件
- **并发安全**: 使用Mutex保护文件操作

### 日志记录：
- 轮转成功时记录info级别日志
- 轮转失败时记录warn级别日志
- 文件操作错误记录到stderr

## 性能优化

### 高效写入：
- 使用`BufWriter`减少系统调用
- 批量刷新缓冲区
- 异步轮转检查（独立线程）

### 内存管理：
- 延迟初始化文件句柄
- 及时释放资源
- 避免内存泄漏

## 测试验证

### 功能测试：
- ✅ 文件大小检查正确
- ✅ 轮转阈值计算准确
- ✅ 备份文件创建成功
- ✅ 新文件创建并写入轮转信息
- ✅ 权限设置正确

### 集成测试：
- ✅ 与现有日志系统兼容
- ✅ 不影响控制台输出
- ✅ 监控线程正常运行
- ✅ 错误处理不影响主程序

## 配置说明

### 可配置参数：
- **最大文件大小**: 当前设置为7MB
- **轮转阈值**: 当前设置为80%
- **检查间隔**: 当前设置为5分钟
- **备份文件数**: 当前保留1个备份

### 修改配置：
如需修改配置，可以在以下位置调整：
- Rust代码: `src/utils/logger.rs`中的常量
- Shell脚本: 各脚本文件中的`MAX_LOG_SIZE_MB`变量

## 兼容性

### 向后兼容：
- ✅ 保持原有的控制台日志输出
- ✅ 保持原有的日志格式
- ✅ 保持原有的日志等级控制
- ✅ Shell脚本轮转机制继续工作

### 系统要求：
- Rust 1.70+
- Android系统支持
- 文件系统写入权限

## 总结

成功实现了完整的日志轮转机制，满足以下要求：
1. ✅ 限制日志文件大小不超过7MB
2. ✅ 自动轮转机制
3. ✅ 保留备份文件
4. ✅ 不影响现有功能
5. ✅ 错误处理完善
6. ✅ 性能优化到位

该实现提供了可靠、高效的日志管理解决方案，确保GPU Governor程序的日志文件不会无限增长，同时保持了良好的可维护性和扩展性。
